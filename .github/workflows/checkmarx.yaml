name: Checkmarx One Scan

# ↓ lock down top‐level permissions to only what we use
permissions:
  contents: read             # we only need to checkout code
  actions: read              # to query workflows/runs
  pull-requests: write       # to comment on or label PRs

on:
  pull_request:
    branches: [ '**' ]
  push:
    branches: [ 'main' ]
  workflow_dispatch: {}   # so you can still run it manually
  schedule:
    - cron: "0 0 * * *"   # run daily at midnight UTC
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true
jobs:
  build:
    permissions:
      contents: read
      pull-requests: write
      security-events: write

    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8

      # TODO: Remove this checkout step once upload-sarif-github-action repo is made public
      # Currently required because GitHub Actions can't directly reference private repos
      - name: Checkout Upload action repository
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8
        with:
          repository: midnightntwrk/upload-sarif-github-action
          ref: 8202d2182e4c0ebec293f9d9140c3378a2afe16e
          path: upload-sarif-github-action
          token: ${{ secrets.MIDNIGHTCI_REPO }}

      # Once public, can simplify to: uses: midnightntwrk/upload-sarif-github-action/checkmarx-scan@8202d2182e4c0ebec293f9d9140c3378a2afe16e
      - name: Checkmarx Full Scan
        uses: ./upload-sarif-github-action/checkmarx-scan
        with:
          cx-client-id: ${{ secrets.CX_CLIENT_ID }}
          cx-client-secret: ${{ secrets.CX_CLIENT_SECRET_EU }}
          cx-tenant: ${{ secrets.CX_TENANT }}
          scs-repo-token: ${{ secrets.MIDNIGHTCI_REPO }}
          upload-to-github: 'true'
