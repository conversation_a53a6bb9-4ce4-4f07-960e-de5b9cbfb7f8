name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  install-and-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      # A bug in npm optional dependencies fails the build, which is fixed in npm 11 or later
      # but the setup-node action isn't using a newer version of npm yet
      # See https://github.com/npm/cli/issues/4828
      - name: Update NPM
        run: |
          npm install -g npm@latest
          npm --version

      # We use --legacy-peer-deps because as of v3.4.1 vite-plugin-wasm doesn't play well with vite 7
      # So this allows them to be installed regardless; They *have* implemented support, they just haven't published a release yet
      # https://github.com/Menci/vite-plugin-wasm/pull/73
      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Install compactc
        env:
          COMPACT_VERSION: 0.24.0
        run: |
          curl --proto '=https' --tlsv1.2 -LsSf https://github.com/midnightntwrk/compact/releases/latest/download/compact-installer.sh | sh
          compact update $COMPACT_VERSION
          compact compile --version

      - name: Compile and test contract
        working-directory: contract
        run: |
          npm run ci

      - name: api
        working-directory: api
        run: |
          npm run ci

      - name: Compile bboard-cli
        working-directory: bboard-cli
        run: |
          npm run ci

      - name: Compile bboard-ui
        working-directory: bboard-ui
        run: |
          npm run ci
