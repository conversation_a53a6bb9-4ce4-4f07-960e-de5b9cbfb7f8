{"name": "@midnight-ntwrk/bboard-api", "version": "0.1.0", "author": "IOG", "license": "MIT", "private": true, "type": "module", "module": "./dist/index.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc --project tsconfig.build.json", "ci": "npm run typecheck && npm run lint && npm run build", "lint": "eslint src", "typecheck": "tsc -p tsconfig.json --noEmit"}}