{"name": "@midnight-ntwrk/bboard-cli", "version": "0.1.0", "author": "IOG", "license": "MIT", "private": true, "type": "module", "scripts": {"build": "rm -rf dist && tsc --project tsconfig.build.json && cp -R ../contract/src/managed dist/contract/src/managed", "ci": "npm run typecheck && npm run lint && npm run build", "lint": "eslint src", "prepack": "npm run build", "standalone": "docker compose -f standalone.yml pull && node --experimental-specifier-resolution=node dist/launcher/standalone.js", "testnet-remote": "node --experimental-specifier-resolution=node --loader ts-node/esm src/launcher/testnet-remote.ts", "testnet-remote-ps": "node --experimental-specifier-resolution=node dist/launcher/testnet-remote-start-proof-server.js", "testnet-local": "node --experimental-specifier-resolution=node dist/launcher/testnet-local.js", "typecheck": "tsc -p tsconfig.json --noEmit"}, "devDependencies": {"@types/json-schema": "^7.0.15", "@types/node": "^24.0.10"}}