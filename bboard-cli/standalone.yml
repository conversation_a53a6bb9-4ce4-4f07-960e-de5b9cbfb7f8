services:
  proof-server:
    container_name: 'bboard-proof-server'
    image: 'midnightnetwork/proof-server:3.0.7'
    ports:
      - '6300'
    environment:
      RUST_BACKTRACE: 'full'
  indexer:
    container_name: 'bboard-indexer'
    image: 'midnightnetwork/midnight-pubsub-indexer:2.3.0'
    platform: linux/amd64
    ports:
      - '0:8088'
    command: ['-Dlogback.configurationFile=logback-json-file.xml']
    environment:
      LOG_LEVEL: 'TRACE'
      LEDGER_NETWORK_ID: 'TestNet'
      SUBSTRATE_NODE_WS_URL: 'ws://node:9944'
      OTEL_JAVAAGENT_ENABLED: 'false'
    healthcheck:
      test: ['CMD', 'egrep', '"Block .* was stored at height 0"', '/tmp/app_logs/server.log']
      interval: 5s
      timeout: 5s
      retries: 30
    depends_on:
      node:
        condition: service_started
  node:
    image: 'midnightnetwork/midnight-node:0.8.0'
    platform: linux/amd64
    container_name: 'bboard-node'
    ports:
      - '9944'
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9944/health']
      interval: 2s
      timeout: 5s
      retries: 5
      start_period: 40s
    environment:
      CFG_PRESET: 'dev'
