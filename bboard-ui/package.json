{"name": "@midnight-ntwrk/bboard-ui", "version": "0.1.0", "author": "IOG", "license": "MIT", "private": true, "type": "module", "scripts": {"build": "rm -rf ./dist && tsc && vite build --mode testnet && cp -r ../contract/src/managed/bboard/keys ./dist/keys && cp -r ../contract/src/managed/bboard/zkir ./dist/zkir", "build:start": "npm run build && npm run start", "ci": "npm run typecheck && npm run lint", "dev": "vite", "lint": "eslint ./src", "start": "http-server --port 0 ./dist", "typecheck": "tsc -p tsconfig.json --noEmit", "preview": "vite preview", "prepack": "yarn build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "node-stdlib-browser": "^1.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "7.6.1"}, "devDependencies": {"@swc/core": "^1.12.9", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.1", "eslint-plugin-react": "^7.37.5", "vite-plugin-node-polyfills": "^0.23.0"}}