// This file is part of midnightntwrk/example-counter.
// Copyright (C) 2025 Midnight Foundation
// SPDX-License-Identifier: Apache-2.0
// Licensed under the Apache License, Version 2.0 (the "License");
// You may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { <PERSON>uffer } from 'buffer';

// While Vite maps the mode that the application is running in by setting either the
// `PROD` or `DEV` variables, we also need to ensure that `NODE_ENV` is set correctly
// because we also use third-party libraries within the browser (such as Apollo Client),
// that might expect it.
//
// @ts-expect-error - support third-party libraries that require `NODE_ENV`.
globalThis.process = {
  env: {
    NODE_ENV: import.meta.env.MODE, // Map `MODE` to `process.env.NODE_ENV`.
  },
};

// We'll also make use of `<PERSON><PERSON>er` objects, so we'll ensure a pollyfill for one is
// present on the global object.
globalThis.Buffer = Buffer;
