// This file is part of midnightntwrk/example-counter.
// Copyright (C) 2025 Midnight Foundation
// SPDX-License-Identifier: Apache-2.0
// Licensed under the Apache License, Version 2.0 (the "License");
// You may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { useContext } from 'react';
import { DeployedBoardContext, type DeployedBoardAPIProvider } from '../contexts';

/**
 * Retrieves the currently in-scope deployed boards provider.
 *
 * @returns The currently in-scope {@link DeployedBBoardAPIProvider} implementation.
 *
 * @internal
 */
export const useDeployedBoardContext = (): DeployedBoardAPIProvider => {
  const context = useContext(DeployedBoardContext);

  if (!context) {
    throw new Error('A <DeployedBoardProvider /> is required.');
  }

  return context;
};
