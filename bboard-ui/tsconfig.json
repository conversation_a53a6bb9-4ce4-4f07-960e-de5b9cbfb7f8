{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "allowJs": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strict": true, "isolatedModules": true, "sourceMap": true, "resolveJsonModule": true, "skipLibCheck": true, "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "esModuleInterop": false, "noEmit": true, "jsx": "react-jsx"}, "include": ["src", "eslint.config.mjs"]}