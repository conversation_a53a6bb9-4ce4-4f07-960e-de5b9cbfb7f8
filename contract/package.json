{"name": "@midnight-ntwrk/bboard-contract", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.js"}}, "scripts": {"build": "rm -rf dist && tsc --project tsconfig.build.json && cp -Rf ./src/managed ./dist/managed && cp ./src/bboard.compact ./dist", "ci": "npm run compact && npm run typecheck && npm run lint && npm run build && npm run test", "compact": "compact compile src/bboard.compact ./src/managed/bboard", "lint": "eslint src", "prepack": "npm build", "test": "vitest", "typecheck": "tsc -p tsconfig.json --noEmit"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.2.0", "typescript": "^5.8.3", "vitest": "^3.2.0"}}