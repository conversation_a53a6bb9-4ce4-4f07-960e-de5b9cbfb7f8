{"name": "@midnight-ntwrk/example-bboard", "version": "0.1.0", "author": "IOG", "license": "MIT", "private": true, "type": "module", "workspaces": {"packages": ["bboard-cli", "bboard-ui", "api", "contract"]}, "dependencies": {"@midnight-ntwrk/compact-runtime": "^0.8.1", "@midnight-ntwrk/dapp-connector-api": "^3.0.0", "@midnight-ntwrk/ledger": "^4.0.0", "@midnight-ntwrk/midnight-js-contracts": "^2.0.2", "@midnight-ntwrk/midnight-js-fetch-zk-config-provider": "^2.0.2", "@midnight-ntwrk/midnight-js-http-client-proof-provider": "^2.0.2", "@midnight-ntwrk/midnight-js-indexer-public-data-provider": "^2.0.2", "@midnight-ntwrk/midnight-js-level-private-state-provider": "^2.0.2", "@midnight-ntwrk/midnight-js-network-id": "^2.0.2", "@midnight-ntwrk/midnight-js-node-zk-config-provider": "^2.0.2", "@midnight-ntwrk/midnight-js-types": "^2.0.2", "@midnight-ntwrk/midnight-js-utils": "^2.0.2", "@midnight-ntwrk/wallet": "^5.0.0", "@midnight-ntwrk/wallet-api": "^5.0.0", "@midnight-ntwrk/wallet-sdk-address-format": "^2.0.0", "@midnight-ntwrk/zswap": "^4.0.0", "buffer": "^6.0.3", "fp-ts": "^2.16.11", "pino": "^9.9.0", "pino-pretty": "^13.1.1", "rxjs": "^7.8.2", "semver": "^7.7.2", "testcontainers": "^11.5.1", "ws": "^8.18.3"}, "devDependencies": {"@eslint/js": "^9.34.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@types/babel__core": "^7.20.5", "@types/semver": "^7.7.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "http-server": "^14.1.1", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0", "vite": "^7.1.4", "vite-plugin-top-level-await": "^1.6.0", "vite-plugin-wasm": "^3.5.0"}}